import requests
import logging
from urllib.parse import quote, urljoin
from django.conf import settings
from typing import List, Dict, Optional
import xml.etree.ElementTree as ET

logger = logging.getLogger(__name__)


class JackettAPIError(Exception):
    """Custom exception for Jackett API errors"""
    pass


class JackettSearchResult:
    """Class to represent a search result from <PERSON><PERSON>"""
    
    def __init__(self, data: Dict):
        self.title = data.get('title', '')
        self.link = data.get('link', '')
        self.guid = data.get('guid', '')
        self.pub_date = data.get('pubDate', '')
        self.description = data.get('description', '')
        self.size = data.get('size', 0)
        self.seeders = data.get('seeders', 0)
        self.peers = data.get('peers', 0)
        self.leechers = max(0, self.peers - self.seeders) if self.peers >= self.seeders else 0
        self.category = data.get('category', '')
        self.indexer = data.get('indexer', '')
        self.download_url = data.get('downloadUrl', '')
        self.magnet_url = data.get('magnetUrl', '')
        
        # Parse quality from title
        self.quality = self._parse_quality(self.title)
        
        # Format size for display
        self.size_formatted = self._format_size(self.size)

        # Score will be calculated later based on quality profile
        self.score = 0
    
    def _parse_quality(self, title: str) -> str:
        """Parse quality information from title"""
        title_upper = title.upper()

        # Check for resolution (order matters for priority)
        if '2160P' in title_upper or '4K' in title_upper or 'UHD' in title_upper:
            return '4K'
        elif '1080P' in title_upper or 'FHD' in title_upper:
            return '1080p'
        elif '720P' in title_upper or 'HD' in title_upper:
            return '720p'
        elif '480P' in title_upper or 'SD' in title_upper:
            return '480p'
        else:
            return 'Unknown'


    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in bytes to human readable format"""
        if size_bytes == 0:
            return 'Unknown'
        
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return {
            'title': self.title,
            'link': self.link,
            'guid': self.guid,
            'pub_date': self.pub_date,
            'description': self.description,
            'size': self.size,
            'size_formatted': self.size_formatted,
            'seeders': self.seeders,
            'leechers': self.leechers,
            'peers': self.peers,
            'quality': self.quality,
            'category': self.category,
            'indexer': self.indexer,
            'download_url': self.download_url,
            'magnet_url': self.magnet_url,
            'score': self.score,
        }


class JackettService:
    """Service for interacting with Jackett API"""
    
    def __init__(self):
        self.base_url = getattr(settings, 'JACKETT_BASE_URL', 'http://localhost:9117')
        self.api_key = getattr(settings, 'JACKETT_API_KEY', '')
        
        if not self.api_key:
            raise JackettAPIError("JACKETT_API_KEY is not configured")
        
        self.session = requests.Session()
        self.session.timeout = 30

    def _get_quality_priority(self, quality: str) -> int:
        """Get numeric priority for quality sorting (higher = better quality)"""
        quality_priorities = {
            '4K': 4,
            '1080p': 3,
            '720p': 2,
            '480p': 1,
            'Unknown': 0
        }
        return quality_priorities.get(quality, 0)

    def calculate_torrent_score(self, torrent: JackettSearchResult, quality_profile=None) -> int:
        """
        Calculate score for a torrent based on quality profile and custom formats

        Args:
            torrent: JackettSearchResult instance
            quality_profile: QualityProfile instance

        Returns:
            Total score for the torrent
        """
        if not quality_profile:
            # Return basic quality priority if no profile
            return self._get_quality_priority(torrent.quality) * 1000

        total_score = 0

        # Base quality score from profile
        quality_score = 0
        if hasattr(quality_profile, 'quality_items'):
            # Find the best matching quality item based on source type
            matching_items = quality_profile.quality_items.filter(
                allowed=True,
                quality__resolution=torrent.quality
            )

            if matching_items.exists():
                # Try to find the most appropriate quality item based on torrent characteristics
                best_item = self._find_best_quality_item(torrent, matching_items)
                if best_item:
                    quality_score = best_item.order * 1000  # Scale up for visibility

        total_score += quality_score

        # Custom format scores
        if hasattr(quality_profile, 'custom_format_scores'):
            for format_score in quality_profile.custom_format_scores.all():
                custom_format = format_score.custom_format

                if self._torrent_matches_custom_format(torrent, custom_format):
                    total_score += format_score.score

        return total_score

    def _find_best_quality_item(self, torrent: JackettSearchResult, quality_items):
        """
        Find the most appropriate quality item for a torrent based on its characteristics

        Args:
            torrent: JackettSearchResult instance
            quality_items: QuerySet of QualityItem objects with same resolution

        Returns:
            Best matching QualityItem or None
        """
        torrent_title_upper = torrent.title.upper()

        # Define source type priorities and their keywords
        source_priorities = [
            ('REMUX', ['REMUX']),
            ('BLURAY', ['BLURAY', 'BLU-RAY', 'BDRIP', 'BDREMUX']),
            ('WEB-DL', ['WEB-DL', 'WEBDL']),
            ('WEBRIP', ['WEBRIP', 'WEB-RIP']),
            ('WEB', ['WEB']),  # Generic WEB (lower priority than WEB-DL)
            ('HDTV', ['HDTV']),
            ('DVDRIP', ['DVDRIP', 'DVD-RIP']),
        ]

        # Try to identify the source type from torrent title
        detected_source = None
        for source_name, keywords in source_priorities:
            if any(keyword in torrent_title_upper for keyword in keywords):
                detected_source = source_name
                break

        # If we can't detect source, use the highest order item
        if not detected_source:
            return quality_items.order_by('-order').first()

        # Try to find a quality item that matches the detected source
        for quality_item in quality_items.order_by('-order'):
            if hasattr(quality_item.quality, 'source'):
                quality_source = quality_item.quality.source.upper()

                # Handle different source matching scenarios
                if detected_source == 'WEB' and quality_source in ['WEB', 'WEB-DL', 'WEBRIP']:
                    # For generic WEB, prefer WEB-DL if available, otherwise any WEB type
                    if quality_source == 'WEB-DL':
                        return quality_item
                elif detected_source == 'WEB-DL' and quality_source == 'WEB-DL':
                    return quality_item
                elif detected_source == 'WEBRIP' and quality_source in ['WEBRIP', 'WEB']:
                    return quality_item
                elif detected_source == quality_source:
                    return quality_item

        # Second pass: for WEB torrents, find any WEB-related quality item
        if detected_source in ['WEB', 'WEB-DL', 'WEBRIP']:
            for quality_item in quality_items.order_by('-order'):
                if hasattr(quality_item.quality, 'source'):
                    if quality_item.quality.source.upper() in ['WEB', 'WEB-DL', 'WEBRIP']:
                        return quality_item

        # If no exact source match, return the item with highest order
        # but prefer lower orders for lower-quality sources
        if detected_source in ['HDTV', 'DVDRIP']:
            return quality_items.order_by('order').first()  # Lowest order for TV/DVD
        else:
            return quality_items.order_by('-order').first()  # Highest order for others

    def _torrent_matches_custom_format(self, torrent: JackettSearchResult, custom_format) -> bool:
        """
        Check if a torrent matches a custom format based on include/exclude patterns

        Args:
            torrent: JackettSearchResult instance
            custom_format: CustomFormat instance

        Returns:
            True if torrent matches the custom format
        """
        import re

        # Combine title and description for pattern matching
        search_text = f"{torrent.title} {torrent.description}"

        # Check include patterns (ANY must match for the format to apply)
        include_patterns = custom_format.include_patterns or []
        if include_patterns:
            any_include_matched = False
            for pattern in include_patterns:
                try:
                    # Remove (?i) inline flag and use re.IGNORECASE instead
                    clean_pattern = pattern.replace('(?i)', '')
                    if re.search(clean_pattern, search_text, re.IGNORECASE):
                        any_include_matched = True
                        break
                except re.error:
                    # If regex is invalid, try simple string matching
                    if pattern.replace('(?i)', '').upper() in search_text.upper():
                        any_include_matched = True
                        break

            # If no include patterns matched, this format doesn't apply
            if not any_include_matched:
                return False

        # Check exclude patterns (NONE must match)
        exclude_patterns = custom_format.exclude_patterns or []
        for pattern in exclude_patterns:
            try:
                # Remove (?i) inline flag and use re.IGNORECASE instead
                clean_pattern = pattern.replace('(?i)', '')
                if re.search(clean_pattern, search_text, re.IGNORECASE):
                    return False
            except re.error:
                # If regex is invalid, try simple string matching
                if pattern.replace('(?i)', '').upper() in search_text.upper():
                    return False

        return True

    def calculate_scores_for_results(self, results: List[JackettSearchResult], quality_profile=None) -> List[JackettSearchResult]:
        """
        Calculate scores for all results and update the score attribute

        Args:
            results: List of JackettSearchResult instances
            quality_profile: QualityProfile instance

        Returns:
            List of results with updated scores
        """
        for result in results:
            result.score = self.calculate_torrent_score(result, quality_profile)

        return results

    def _filter_episode_results(self, results: List[JackettSearchResult], show_title: str, season: int, episode: int) -> List[JackettSearchResult]:
        """Filter results to only include those that match the specific episode and show"""
        filtered_results = []

        # Create patterns to match
        season_episode_patterns = [
            f"S{season:02d}E{episode:02d}",
            f"s{season:02d}e{episode:02d}",
            f"{season}x{episode:02d}",
            f"Season {season} Episode {episode}",
            f"S{season}E{episode}",
        ]

        # Normalize show title for better matching
        normalized_show_title = self._normalize_title(show_title)

        for result in results:
            title_upper = result.title.upper()

            # Check if title contains episode pattern first
            episode_match = False
            for pattern in season_episode_patterns:
                if pattern.upper() in title_upper:
                    episode_match = True
                    break

            if not episode_match:
                continue

            # Now check if the show title matches (most important check)
            if not self._title_matches_show(result.title, show_title, normalized_show_title):
                logger.debug(f"Rejecting '{result.title}' - show title mismatch with '{show_title}'")
                continue

            filtered_results.append(result)

        return filtered_results

    def _normalize_title(self, title: str) -> str:
        """Normalize a title for comparison by removing common variations"""
        import re

        # Convert to lowercase
        normalized = title.lower()

        # Remove common punctuation and replace with spaces
        normalized = re.sub(r'[^\w\s]', ' ', normalized)

        # Remove common words that cause issues
        common_words = ['the', 'a', 'an', 'and', 'or', 'of', 'in', 'on', 'at', 'to', 'for', 'with']
        words = normalized.split()
        words = [word for word in words if word not in common_words]

        # Join back and remove extra spaces
        normalized = ' '.join(words)
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        return normalized

    def _title_matches_show(self, torrent_title: str, original_show_title: str, normalized_show_title: str) -> bool:
        """
        Check if a torrent title matches the show title using Sonarr-inspired logic

        This implements a strict matching algorithm similar to what Sonarr uses to avoid
        false positives like "The Assassin" matching "The Shy Hero and the Assassin Princesses"
        """
        import re

        # Clean the torrent title to extract just the show name part
        torrent_show_name = self._extract_show_name_from_torrent(torrent_title)

        # Method 1: Exact match (case insensitive)
        if torrent_show_name.lower() == original_show_title.lower():
            return True

        # Method 2: Normalized exact match
        normalized_torrent_show = self._normalize_title(torrent_show_name)
        if normalized_torrent_show == normalized_show_title:
            return True

        # Method 3: Handle "The" prefix variations
        show_without_the = self._remove_the_prefix(original_show_title)
        torrent_without_the = self._remove_the_prefix(torrent_show_name)

        if show_without_the.lower() == torrent_without_the.lower():
            return True

        # Method 4: Handle year variations (e.g., "The Valley 2024" vs "The Valley")
        # Remove years from both titles and compare
        show_without_year = re.sub(r'\s+\d{4}$', '', original_show_title.strip())
        torrent_without_year = re.sub(r'\s+\d{4}$', '', torrent_show_name.strip())

        if show_without_year.lower() == torrent_without_year.lower():
            return True

        # Also try the reverse - torrent might have year, show might not
        if original_show_title.lower() == torrent_without_year.lower():
            return True

        # Try with normalized titles and year removal
        normalized_show_without_year = self._normalize_title(show_without_year)
        normalized_torrent_without_year = self._normalize_title(torrent_without_year)

        if normalized_show_without_year == normalized_torrent_without_year:
            return True

        # Method 5: Check if it's a partial match that should be rejected
        # This is the key to avoiding false positives
        if self._is_partial_title_match(torrent_show_name, original_show_title):
            return False

        return False

    def _extract_show_name_from_torrent(self, torrent_title: str) -> str:
        """Extract just the show name from a torrent title, removing season/episode/quality info"""
        import re

        # Common patterns that indicate the end of the show name
        end_patterns = [
            r'\s+S\d{1,2}E\d{1,2}',  # S01E01
            r'\s+S\d{1,2}',          # S01
            r'\s+Season\s+\d+',      # Season 1
            r'\s+\d{4}p',            # 1080p, 720p
            r'\s+\d{3,4}x\d{3,4}',   # 1920x1080
            r'\s+BluRay',            # BluRay
            r'\s+WEB-?DL',           # WEB-DL, WEBDL
            r'\s+WEB-?Rip',          # WEB-Rip, WEBRip
            r'\s+HDTV',              # HDTV
            r'\s+x26[45]',           # x264, x265
            r'\s+H\.?26[45]',        # H.264, H264, H.265, H265
            r'\s+HEVC',              # HEVC
            r'\s+AAC',               # AAC
            r'\s+AC3',               # AC3
            r'\s+DTS',               # DTS
        ]

        # Find the first match and cut the title there
        for pattern in end_patterns:
            match = re.search(pattern, torrent_title, re.IGNORECASE)
            if match:
                return torrent_title[:match.start()].strip()

        # If no pattern found, return the whole title (might be just the show name)
        return torrent_title.strip()

    def _remove_the_prefix(self, title: str) -> str:
        """Remove 'The' prefix from title if present"""
        if title.lower().startswith('the '):
            return title[4:].strip()
        return title

    def _is_partial_title_match(self, torrent_show_name: str, original_show_title: str) -> bool:
        """
        Check if the torrent contains the show title as a partial match that should be rejected

        Examples:
        - "The Shy Hero and the Assassin Princesses" contains "The Assassin" but should be rejected
        - "House of Cards" contains "House" but should be rejected
        - "Breaking Bad Better Call Saul" contains "Breaking Bad" but should be rejected
        """
        import re

        # Normalize both titles
        torrent_normalized = self._normalize_title(torrent_show_name.lower())
        show_normalized = self._normalize_title(original_show_title.lower())

        # If the show title appears in the torrent title but they're not equal,
        # it's likely a partial match that should be rejected
        if show_normalized in torrent_normalized and show_normalized != torrent_normalized:
            # Additional checks to see if it's a legitimate partial match

            # Check if the show title appears at the start with word boundaries
            if torrent_normalized.startswith(show_normalized + ' '):
                # This could be legitimate (e.g., "Breaking Bad S01E01")
                # or illegitimate (e.g., "Breaking Bad Better Call Saul")

                # Get what comes after the show title
                after_show = torrent_normalized[len(show_normalized):].strip()
                words_after = after_show.split()

                if words_after:
                    first_word = words_after[0]

                    # If followed by season/episode info, it's legitimate
                    if re.match(r's\d+', first_word) or first_word in ['season', 'series']:
                        return False  # Not a partial match, it's legitimate

                    # If followed by quality info, it's legitimate
                    if first_word in ['1080p', '720p', '480p', '2160p', '4k', 'bluray', 'webrip', 'webdl', 'hdtv']:
                        return False  # Not a partial match, it's legitimate

                    # Otherwise, it's likely part of a longer title
                    return True  # This is a partial match, reject it

            # Check if the show title appears elsewhere in the torrent title
            # This is almost always a partial match that should be rejected
            return True

        return False  # Not a partial match

    def _filter_season_results(self, results: List[JackettSearchResult], show_title: str, season: int) -> List[JackettSearchResult]:
        """Filter results to only include those that match the complete season"""
        filtered_results = []

        # Create patterns to match season packs
        season_patterns = [
            f"S{season:02d}",
            f"s{season:02d}",
            f"Season {season}",
            f"SEASON {season}",
            f"S{season}",
        ]

        # Patterns that indicate it's NOT a season pack (individual episodes)
        exclude_patterns = [
            f"S{season:02d}E",
            f"s{season:02d}e",
            f"{season}x",
        ]

        for result in results:
            title_upper = result.title.upper()

            # Check if title contains the show name
            if show_title.upper() not in title_upper:
                continue

            # Check if it's an individual episode (exclude these)
            is_individual_episode = False
            for exclude_pattern in exclude_patterns:
                if exclude_pattern.upper() in title_upper:
                    is_individual_episode = True
                    break

            if is_individual_episode:
                continue

            # Check if title contains season pattern
            season_match = False
            for pattern in season_patterns:
                if pattern.upper() in title_upper:
                    season_match = True
                    break

            # Also check for "complete" or "pack" keywords
            if any(keyword in title_upper for keyword in ['COMPLETE', 'PACK', 'COLLECTION']):
                season_match = True

            if season_match:
                filtered_results.append(result)

        return filtered_results

    def search(self, query: str, indexer: str = 'all', category: str = '5000') -> List[JackettSearchResult]:
        """
        Search for torrents using Jackett API
        
        Args:
            query: Search query
            indexer: Indexer to search (default: 'all')
            category: Category to search in (default: '5000' for TV)
        
        Returns:
            List of JackettSearchResult objects
        """
        try:
            # Construct the search URL using Torznab API
            if indexer == 'all':
                endpoint = '/api/v2.0/indexers/all/results/torznab/'
            else:
                endpoint = f'/api/v2.0/indexers/{indexer}/results/torznab/'

            url = urljoin(self.base_url, endpoint)
            
            # Prepare parameters
            params = {
                'apikey': self.api_key,
                't': 'search',
                'q': query,
                'cat': category,
            }
            
            logger.info(f"Searching Jackett: {query} on {indexer}")
            
            # Make the request
            response = self.session.get(url, params=params)
            response.raise_for_status()

            # Check response content type and content
            content_type = response.headers.get('content-type', '').lower()
            response_text = response.text.strip()

            logger.debug(f"Jackett response content-type: {content_type}")
            logger.debug(f"Jackett response length: {len(response_text)}")

            # Handle empty response
            if not response_text:
                logger.warning("Jackett returned empty response")
                return []

            # Check if response is JSON (error response)
            if 'application/json' in content_type or response_text.startswith('{'):
                try:
                    import json
                    error_data = json.loads(response_text)
                    error_msg = error_data.get('error', 'Unknown error from Jackett')
                    logger.error(f"Jackett API error: {error_msg}")
                    logger.error(f"Full Jackett response: {response_text}")
                    raise JackettAPIError(f"Jackett API error: {error_msg}")
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON response from Jackett: {e}")
                    logger.error(f"Response content: {response_text[:500]}")
                    raise JackettAPIError(f"Invalid response format from Jackett: {response_text[:100]}")

            # Check if response looks like XML
            if not response_text.startswith('<?xml') and not response_text.startswith('<'):
                logger.error(f"Unexpected response format from Jackett: {response_text[:200]}")
                raise JackettAPIError(f"Unexpected response format from Jackett. Response: {response_text[:100]}")

            # Parse the XML response
            results = self._parse_xml_response(response_text)

            logger.info(f"Found {len(results)} results for query: {query}")
            return results
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Jackett API request failed: {e}")
            raise JackettAPIError(f"API request failed: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in Jackett search: {e}")
            raise JackettAPIError(f"Unexpected error: {e}")
    
    def search_episode(self, show_title: str, season: int, episode: int, indexer: str = 'all', filter_results: bool = True) -> List[JackettSearchResult]:
        """
        Search for a specific TV episode
        
        Args:
            show_title: Title of the TV show
            season: Season number
            episode: Episode number
            indexer: Indexer to search (default: 'all')
        
        Returns:
            List of JackettSearchResult objects
        """
        # Format the search query for episode search
        # Try multiple formats to increase chances of finding results
        queries = [
            f"{show_title} S{season:02d}E{episode:02d}",
            f"{show_title} {season}x{episode:02d}",
            f"{show_title} Season {season} Episode {episode}",
        ]
        
        all_results = []
        
        for query in queries:
            try:
                results = self.search(query, indexer, category='5000')  # TV category
                all_results.extend(results)
            except JackettAPIError as e:
                logger.warning(f"Search failed for query '{query}': {e}")
                continue
        
        # Remove duplicates based on guid
        seen_guids = set()
        unique_results = []
        for result in all_results:
            if result.guid not in seen_guids:
                seen_guids.add(result.guid)
                unique_results.append(result)

        # Apply filtering if requested
        if filter_results:
            unique_results = self._filter_episode_results(unique_results, show_title, season, episode)

        # Sort by quality first (highest quality first), then by seeders (descending)
        unique_results.sort(key=lambda x: (self._get_quality_priority(x.quality), x.seeders), reverse=True)

        return unique_results

    def search_season(self, show_title: str, season: int, indexer: str = 'all', filter_results: bool = True) -> List[JackettSearchResult]:
        """
        Search for a complete TV season

        Args:
            show_title: Title of the TV show
            season: Season number
            indexer: Indexer to search (default: 'all')

        Returns:
            List of JackettSearchResult objects
        """
        # Format the search query for season search
        # Try multiple formats to increase chances of finding results
        queries = [
            f"{show_title} S{season:02d}",
            f"{show_title} Season {season}",
            f"{show_title} Complete Season {season}",
            f"{show_title} S{season:02d} Complete",
        ]

        all_results = []

        for query in queries:
            try:
                results = self.search(query, indexer, category='5000')  # TV category
                all_results.extend(results)
            except JackettAPIError as e:
                logger.warning(f"Season search failed for query '{query}': {e}")
                continue

        # Remove duplicates based on guid
        seen_guids = set()
        unique_results = []
        for result in all_results:
            if result.guid not in seen_guids:
                seen_guids.add(result.guid)
                unique_results.append(result)

        # Apply filtering if requested
        if filter_results:
            unique_results = self._filter_season_results(unique_results, show_title, season)

        # Sort by quality first (highest quality first), then by seeders (descending)
        unique_results.sort(key=lambda x: (self._get_quality_priority(x.quality), x.seeders), reverse=True)

        return unique_results

    def select_best_torrent(self, results: List[JackettSearchResult], quality_profile=None) -> JackettSearchResult:
        """
        Select the best torrent from results based on quality profile

        Args:
            results: List of search results
            quality_profile: QualityProfile instance to filter by

        Returns:
            Best JackettSearchResult or None if no suitable torrent found
        """
        if not results:
            return None

        # If no profile specified, return highest quality with most seeders
        if not quality_profile:
            return max(results, key=lambda x: (self._get_quality_priority(x.quality), x.seeders))

        # Filter results based on quality profile
        allowed_qualities = []
        if hasattr(quality_profile, 'quality_items'):
            allowed_qualities = [
                item.quality.resolution for item in quality_profile.quality_items.filter(allowed=True)
            ]

        # If no allowed qualities defined, allow all
        if not allowed_qualities:
            filtered_results = results
        else:
            filtered_results = [
                result for result in results
                if result.quality in allowed_qualities or result.quality == 'Unknown'
            ]

        if not filtered_results:
            return None

        # Sort by quality priority (higher is better), then by seeders (more is better)
        best_torrent = max(filtered_results, key=lambda x: (self._get_quality_priority(x.quality), x.seeders))

        return best_torrent

    def _parse_xml_response(self, xml_content: str) -> List[JackettSearchResult]:
        """Parse XML response from Jackett API"""
        try:
            root = ET.fromstring(xml_content)
            results = []
            
            # Find all item elements
            for item in root.findall('.//item'):
                result_data = {}
                
                # Extract basic fields
                result_data['title'] = self._get_element_text(item, 'title')
                result_data['link'] = self._get_element_text(item, 'link')
                result_data['guid'] = self._get_element_text(item, 'guid')
                result_data['pubDate'] = self._get_element_text(item, 'pubDate')
                result_data['description'] = self._get_element_text(item, 'description')
                
                # Extract torznab attributes
                for attr in item.findall('.//{http://torznab.com/schemas/2015/feed}attr'):
                    name = attr.get('name')
                    value = attr.get('value')

                    if name == 'size':
                        result_data['size'] = int(value) if value and value.isdigit() else 0
                    elif name == 'seeders':
                        result_data['seeders'] = int(value) if value and value.isdigit() else 0
                    elif name == 'peers':
                        result_data['peers'] = int(value) if value and value.isdigit() else 0
                    elif name == 'downloadvolumefactor':
                        result_data['downloadvolumefactor'] = float(value) if value else 1.0
                    elif name == 'uploadvolumefactor':
                        result_data['uploadvolumefactor'] = float(value) if value else 1.0

                # Also check for size in the <size> element (some indexers use this)
                size_element = item.find('size')
                if size_element is not None and size_element.text:
                    try:
                        result_data['size'] = int(size_element.text)
                    except ValueError:
                        pass
                
                # Extract enclosure URL (download link)
                enclosure = item.find('enclosure')
                if enclosure is not None:
                    result_data['downloadUrl'] = enclosure.get('url', '')
                
                # Extract category
                category = item.find('category')
                if category is not None:
                    result_data['category'] = category.text or ''
                
                # Try to extract indexer from jackettindexer element first
                jackett_indexer = item.find('jackettindexer')
                if jackett_indexer is not None and jackett_indexer.text:
                    result_data['indexer'] = jackett_indexer.text
                else:
                    # Fallback: try to extract indexer from comments or other fields
                    comments = self._get_element_text(item, 'comments')
                    if comments:
                        # Extract indexer name from comments URL
                        try:
                            from urllib.parse import urlparse
                            parsed = urlparse(comments)
                            result_data['indexer'] = parsed.netloc.split('.')[0] if parsed.netloc else 'Unknown'
                        except:
                            result_data['indexer'] = 'Unknown'
                    else:
                        result_data['indexer'] = 'Unknown'
                
                results.append(JackettSearchResult(result_data))
            
            return results
            
        except ET.ParseError as e:
            logger.error(f"Failed to parse XML response: {e}")
            raise JackettAPIError(f"Failed to parse XML response: {e}")
    
    def _get_element_text(self, parent, tag_name: str) -> str:
        """Safely get text content of an XML element"""
        element = parent.find(tag_name)
        return element.text if element is not None and element.text else ''
    
    def test_connection(self) -> bool:
        """Test connection to Jackett API"""
        try:
            # Test with a simple search to verify the API is working
            url = urljoin(self.base_url, '/api/v2.0/indexers/all/results/torznab/')
            params = {
                'apikey': self.api_key,
                't': 'search',
                'q': 'test',
                'cat': '5000',
                'limit': '1'
            }

            logger.info(f"Testing Jackett connection to: {url}")

            response = self.session.get(url, params=params, timeout=10)

            # Log response details for debugging
            logger.debug(f"Response status: {response.status_code}")
            logger.debug(f"Response headers: {dict(response.headers)}")
            logger.debug(f"Response content: {response.text[:200]}")

            response.raise_for_status()

            # Check if response is valid XML (Torznab returns XML)
            response_text = response.text.strip()
            content_type = response.headers.get('content-type', '').lower()

            logger.debug(f"Response content-type: {content_type}")
            logger.debug(f"Response length: {len(response_text)}")

            # Check if it's XML
            if response_text.startswith('<?xml') or response_text.startswith('<'):
                try:
                    # Try to parse the XML to verify it's valid
                    root = ET.fromstring(response_text)
                    logger.info("Successfully connected to Jackett API (XML response received)")
                    return True
                except ET.ParseError as e:
                    logger.error(f"Invalid XML response from Jackett: {e}")
                    logger.error(f"Response content: {response_text[:500]}")
                    return False
            else:
                logger.error(f"Unexpected response format from Jackett")
                logger.error(f"Response content: {response_text[:500]}")
                logger.error(f"Response status: {response.status_code}")
                logger.error(f"Response headers: {dict(response.headers)}")
                return False

        except requests.exceptions.ConnectionError as e:
            logger.error(f"Cannot connect to Jackett at {self.base_url}: {e}")
            return False
        except requests.exceptions.Timeout as e:
            logger.error(f"Jackett connection timeout: {e}")
            return False
        except requests.exceptions.HTTPError as e:
            logger.error(f"Jackett HTTP error {response.status_code}: {e}")
            if response.status_code == 401:
                logger.error("Authentication failed - check your API key")
            elif response.status_code == 404:
                logger.error("Jackett API endpoint not found - check your base URL")
            return False
        except Exception as e:
            logger.error(f"Jackett connection test failed: {e}")
            return False
    
    def get_indexers(self) -> List[Dict]:
        """Get list of configured indexers"""
        try:
            url = urljoin(self.base_url, '/api/v2.0/indexers')
            params = {'apikey': self.api_key}
            
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            # Parse JSON response
            indexers_data = response.json()
            
            indexers = []
            for indexer in indexers_data:
                indexers.append({
                    'id': indexer.get('id', ''),
                    'name': indexer.get('name', ''),
                    'description': indexer.get('description', ''),
                    'type': indexer.get('type', ''),
                    'configured': indexer.get('configured', False),
                })
            
            return indexers
            
        except Exception as e:
            logger.error(f"Failed to get indexers: {e}")
            return []
